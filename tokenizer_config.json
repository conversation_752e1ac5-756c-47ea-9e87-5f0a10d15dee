{"add_prefix_space": false, "added_tokens_decoder": {"50256": {"content": "<|endoftext|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "50257": {"content": "                               ", "lstrip": false, "normalized": true, "rstrip": false, "single_word": false, "special": false}, "50258": {"content": "                              ", "lstrip": false, "normalized": true, "rstrip": false, "single_word": false, "special": false}, "50259": {"content": "                             ", "lstrip": false, "normalized": true, "rstrip": false, "single_word": false, "special": false}, "50260": {"content": "                            ", "lstrip": false, "normalized": true, "rstrip": false, "single_word": false, "special": false}, "50261": {"content": "                           ", "lstrip": false, "normalized": true, "rstrip": false, "single_word": false, "special": false}, "50262": {"content": "                          ", "lstrip": false, "normalized": true, "rstrip": false, "single_word": false, "special": false}, "50263": {"content": "                         ", "lstrip": false, "normalized": true, "rstrip": false, "single_word": false, "special": false}, "50264": {"content": "                        ", "lstrip": false, "normalized": true, "rstrip": false, "single_word": false, "special": false}, "50265": {"content": "                       ", "lstrip": false, "normalized": true, "rstrip": false, "single_word": false, "special": false}, "50266": {"content": "                      ", "lstrip": false, "normalized": true, "rstrip": false, "single_word": false, "special": false}, "50267": {"content": "                     ", "lstrip": false, "normalized": true, "rstrip": false, "single_word": false, "special": false}, "50268": {"content": "                    ", "lstrip": false, "normalized": true, "rstrip": false, "single_word": false, "special": false}, "50269": {"content": "                   ", "lstrip": false, "normalized": true, "rstrip": false, "single_word": false, "special": false}, "50270": {"content": "                  ", "lstrip": false, "normalized": true, "rstrip": false, "single_word": false, "special": false}, "50271": {"content": "                 ", "lstrip": false, "normalized": true, "rstrip": false, "single_word": false, "special": false}, "50272": {"content": "                ", "lstrip": false, "normalized": true, "rstrip": false, "single_word": false, "special": false}, "50273": {"content": "               ", "lstrip": false, "normalized": true, "rstrip": false, "single_word": false, "special": false}, "50274": {"content": "              ", "lstrip": false, "normalized": true, "rstrip": false, "single_word": false, "special": false}, "50275": {"content": "             ", "lstrip": false, "normalized": true, "rstrip": false, "single_word": false, "special": false}, "50276": {"content": "            ", "lstrip": false, "normalized": true, "rstrip": false, "single_word": false, "special": false}, "50277": {"content": "           ", "lstrip": false, "normalized": true, "rstrip": false, "single_word": false, "special": false}, "50278": {"content": "          ", "lstrip": false, "normalized": true, "rstrip": false, "single_word": false, "special": false}, "50279": {"content": "         ", "lstrip": false, "normalized": true, "rstrip": false, "single_word": false, "special": false}, "50280": {"content": "        ", "lstrip": false, "normalized": true, "rstrip": false, "single_word": false, "special": false}, "50281": {"content": "       ", "lstrip": false, "normalized": true, "rstrip": false, "single_word": false, "special": false}, "50282": {"content": "      ", "lstrip": false, "normalized": true, "rstrip": false, "single_word": false, "special": false}, "50283": {"content": "     ", "lstrip": false, "normalized": true, "rstrip": false, "single_word": false, "special": false}, "50284": {"content": "    ", "lstrip": false, "normalized": true, "rstrip": false, "single_word": false, "special": false}, "50285": {"content": "   ", "lstrip": false, "normalized": true, "rstrip": false, "single_word": false, "special": false}, "50286": {"content": "  ", "lstrip": false, "normalized": true, "rstrip": false, "single_word": false, "special": false}, "50287": {"content": "\t\t\t\t\t\t\t\t\t", "lstrip": false, "normalized": true, "rstrip": false, "single_word": false, "special": false}, "50288": {"content": "\t\t\t\t\t\t\t\t", "lstrip": false, "normalized": true, "rstrip": false, "single_word": false, "special": false}, "50289": {"content": "\t\t\t\t\t\t\t", "lstrip": false, "normalized": true, "rstrip": false, "single_word": false, "special": false}, "50290": {"content": "\t\t\t\t\t\t", "lstrip": false, "normalized": true, "rstrip": false, "single_word": false, "special": false}, "50291": {"content": "\t\t\t\t\t", "lstrip": false, "normalized": true, "rstrip": false, "single_word": false, "special": false}, "50292": {"content": "\t\t\t\t", "lstrip": false, "normalized": true, "rstrip": false, "single_word": false, "special": false}, "50293": {"content": "\t\t\t", "lstrip": false, "normalized": true, "rstrip": false, "single_word": false, "special": false}, "50294": {"content": "\t\t", "lstrip": false, "normalized": true, "rstrip": false, "single_word": false, "special": false}}, "bos_token": "<|endoftext|>", "clean_up_tokenization_spaces": true, "eos_token": "<|endoftext|>", "extra_special_tokens": {}, "model_max_length": 2048, "pad_token": "<|endoftext|>", "return_token_type_ids": false, "tokenizer_class": "CodeGenTokenizer", "unk_token": "<|endoftext|>"}